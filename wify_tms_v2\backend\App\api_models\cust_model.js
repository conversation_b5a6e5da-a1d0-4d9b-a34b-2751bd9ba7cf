var sampleOperationResp = require('../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../api_models/utils/db_resp');
const users_model = require('./users_model');

class cust_model {
    createOrUpdateBatch(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['batch_data'] = query.batch_data;

            var form_data = JSON.stringify(query);
            // console.log("form_data",form_data);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log("Form data to function",form_data);
            this.db.tms_create_cust_batch(form_data).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_create_cust_batch);

                    if (dbResp.code == 'cust_already_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Customer email or mobile should be unique for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (
                        dbResp.code == 'cust_already_exists_name_address'
                    ) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Customer with this name and address combination already exists for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSingleEntry(query, entry_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_cust_details(entry_id).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_cust_details);

                    console.log('dbResp cust', dbResp);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        this.getViewDataFrCustForm({}).then((operationResp) => {
                            if (operationResp.isSuccess()) {
                                var finalResp = JSON.parse(operationResp.resp);
                                // console.log(dbResp.data);
                                finalResp.form_data = dbResp.data;
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(finalResp),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else {
                                resolve(operationResp);
                            }
                        });
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdateCust(query, entry_id = null) {
        // check if form fields is empty
        // Yes --> return Bad request (please fill * mandatory field)

        // check if email and phone exists in same org_id from cl_tx_cust table
        // Yes --> return conflict (customer email and phone should be unique for the same organization)

        // if insert successfully
        // Yes --> return HttpStatus.StatusCodes.OK and entry_id
        // No --> return Internal server error status= false and message = Internal_error

        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);

            if (!this.validateCustForm(form_data)) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Please fill mandatory * field',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_create_cust(form_data, entry_id).then(
                (res) => {
                    var custViewResp = new db_resp(res[0].tms_create_cust);

                    if (custViewResp.code == 'cust_already_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Customer email or mobile should be unique for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (
                        custViewResp.code == 'cust_already_exists_name_address'
                    ) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Customer with this name and address combination already exists for the same organization',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (!custViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(custViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAllCustomers(form) {
        if (Object.keys(form).length != 0) {
            var pagination = JSON.parse(form.pagination);

            var org_id = users_model.getOrgId(this.userContext);
            var page_no = pagination.current;
            var page_size = pagination.pageSize;
            var search_query = form.search_query;

            //Filters remove -1 in from array
            var filters = JSON.parse(form.filters);
            var filtersAllKeys = Object.keys(filters);
            for (var i = 0; i < filtersAllKeys.length; i++) {
                // if (filters[filtersAllKeys[i]][0] == '-1') {
                //     filters[filtersAllKeys[i]].pop();
                // }
                if (
                    filters[filtersAllKeys[i]]?.length > 0 &&
                    Array.isArray(filters[filtersAllKeys[i]]) &&
                    filters[filtersAllKeys[i]].includes('-1')
                ) {
                    delete filters[filtersAllKeys[i]];
                }
            }
            filters = JSON.stringify(filters);
        }

        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_customers(
                    org_id,
                    page_no,
                    page_size,
                    filters,
                    search_query
                )
                .then(
                    (res) => {
                        var custResp = new db_resp(res[0].tms_get_customers);

                        if (!custResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(custResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getViewDataFrCustForm(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('getViewDataFrUserForm ', form_data);
            this.db.tms_get_customer_proto(form_data).then(
                (res) => {
                    var userViewResp = new db_resp(
                        res[0].tms_get_customer_proto
                    );

                    if (!userViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(userViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    validateCustForm(form_data) {
        return true;
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
    getFreshInstance(model) {
        const clonedInstance = new cust_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
    getInstance() {
        const instance = new cust_model();
        return instance;
    }
}

module.exports = new cust_model();
