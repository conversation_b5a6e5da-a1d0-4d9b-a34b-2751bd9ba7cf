CREATE OR REPLACE FUNCTION public.tms_hlpr_get_org_settings_overview_proto(org_id_ integer)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	resp_data json;
begin
	select json_build_object(
			'country_code',	org_settings.settings_data->>'select_consumer_phone_number_country_code',
		    'mobile_digit', (org_settings.settings_data->>'select_number_of_digits_for_the_phone_number')::int,
			'select_country_code',settings_data->>'select_country_code',
			'selected_country_pincode_length',(settings_data->>'selected_country_pincode_length')::int,
			'country_name',sys_country_info.country,
			'enable_customer_profile_creation_based_on_name',(org_settings.settings_data->>'enable_customer_profile_creation_based_on_name')::boolean
		)
	  from public.cl_tx_orgs_settings as org_settings
	  inner join sys_cf_country_info as sys_country_info 
	  	on sys_country_info.country_code = settings_data->>'select_country_code'
	 where org_settings.org_id = org_id_
       and org_settings.settings_type = 'ORG_LEVEL_SETTINGS'
      into resp_data;
    return resp_data;
end;

$function$
;

