import React from 'react';
import RemoteSourceSelect from '../components/wify-utils/RemoteSourceSelect';
import http_utils from './http_utils';
import LocationSearchInput from '../components/LocationSearchInput';
import { Button } from 'antd';
import axios from 'axios';
import RefreshGeoLocation from '../routes/services/RefreshGeoLocation';
import debounce from "lodash/debounce"

const google_map_api_key = process.env.REACT_APP_GOOGLE_MAP_API_KEY;
export const getCustomerDetailsFrmApi = (phoneNumber, resolve) => {
    let url = '/searcher';
    let params = {
        fn: 'getCustomers',
    };
    http_utils.performGetCall(
        url,
        {
            ...params,
            query: phoneNumber,
        },
        (resp) => {
            // console.log("Api data - ",resp);
            resolve(resp.data);
        },
        (error) => {
            // console.log("Api error - ",error);
            resolve(false);
        }
    );
};

export const getAddressFieldKeys = (prefix) => {
    return [
        `${prefix}line_0`,
        `${prefix}line_1`,
        `${prefix}line_2`,
        `${prefix}line_3`,
        `${prefix}pincode`,
        `${prefix}city`,
        `${prefix}state`,
        `location_latitude`,
        `location_Longitude`,
        // `${prefix}mobile`
    ];
};

export const getCityStateWithPincode = (query) => {
    const url = '/searcher';
    const params = {
        fn: 'getPincode',
    };
    return new Promise((resolve, reject) => {
        http_utils.performGetCall(
            url,
            {
                ...params,
                query: query,
            },
            (resp) => {
                resolve(resp.data); // Return the response as an object
            },
            (error) => {
                // Return an empty object in case of an error
                resolve({ data: [] });
            }
        );
    });
};

export const searchCustomersByCombinedNameAddress = (formRef, onCustomerSelect = null) => {
    console.log("Searching------------------------")
    if (!formRef?.current) return;

    const formData = formRef.current.getFieldsValue();
    if(!formData.cust_full_name) return;

    // Collect name and address fields in specific order
    const combinedQuery = [
        formData.cust_full_name || '',
        formData.cust_line_0 || '',
        formData.cust_line_1 || '',
        formData.cust_line_2 || '',
        formData.cust_line_3 || '',
        formData.cust_city || '',
        formData.cust_state || '',
        formData.cust_pincode || ''
    ].join(",");
    console.log('Combined search query:', combinedQuery);

    // Call the searcher API with new combined key
    http_utils.performGetCall(
        '/searcher',
        {
            fn: 'getCustomers',
            combined_search: combinedQuery  // New key for combined search
        },
        (resp) => {
            const parsedData = typeof resp.data === 'string'
                ? JSON.parse(resp.data)
                : resp.data;

            const customers = parsedData || [];
            console.log({customersFound: customers});

            // Auto-select the first customer found if available
            if (customers.length > 0) {
                const firstCustomer = customers[0];
                onCustomerSelect(firstCustomer);
            }
        },
        (error) => {
            console.error('Combined customer search failed', error);
        }
    );
};

export const addressFill = (value, formRef, prefix = 'cust_') => {
    if (value?.postalCode) {
        getCityStateWithPincode(value?.postalCode)
            .then((searcherResp) => {
                formRef.current.setFieldsValue({
                    [`${prefix}city`]: searcherResp[0]?.city || '',
                    [`${prefix}state`]: searcherResp[0]?.state || '',
                });
            })
            .catch((error) => {
                console.error('Error fetching search results:', error);
                formRef.current.setFieldsValue({
                    [`${prefix}city`]: value?.city,
                    [`${prefix}state`]: value?.state,
                });
            });
    } else {
        formRef.current.setFieldsValue({
            [`${prefix}city`]: '',
            [`${prefix}state`]: '',
            [`${prefix}pincode`]: '',
        });
    }

    formRef.current.setFieldsValue({
        [`${prefix}line_0`]: '',
        [`${prefix}line_1`]: value?.building,
        [`${prefix}line_2`]: value?.street,
        [`${prefix}line_3`]: value?.door,
        [`${prefix}pincode`]: value?.postalCode,
        location_latitude: value?.location?.latlng?.lat,
        location_Longitude: value?.location?.latlng?.lng,
    });
};

export const getAddressBasedOnLatAndLng = async (latitude, longitude) => {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${google_map_api_key}`;
    const response = await axios.get(url);
    return response.data;
};
export const getConcatenatedAddressFrmForm = (prefix, formRef) => {
    let allFieldsValue = formRef?.current?.getFieldsValue(true);
    // console.log('allFieldsValue', allFieldsValue);
    if (allFieldsValue) {
        let addressFieldKeys = getAddressFieldKeys(prefix);
        return addressFieldKeys.filter((key) => allFieldsValue[key]).toString();
    }
};

export const getAddressFieldsMeta = (
    formRef,
    forceUpdateFn,
    is_pincode_mandatory = false,
    orgSettingsData,
    editMode,
    editorItem,
    onChange,
    cust_pincode,
    isConfigEnabled = false,
    onCustomerSelect = null
) => {
    let clearGoogleAddressSearch = new Date().getTime();
    let filledAddress = getConcatenatedAddressFrmForm('cust_', formRef);
    let showClearFieldsButton = filledAddress && filledAddress != '';
    const clearAddress = (formRef) => {
        const keyEmptyValue = {};
        getAddressFieldKeys('cust_').forEach((singleKey) => {
            keyEmptyValue[singleKey] = '';
        });
        clearGoogleAddressSearch = new Date().getTime();
        let result = formRef?.current?.setFieldsValue(keyEmptyValue);
        if (forceUpdateFn) {
            forceUpdateFn();
        }
    };

    // Create a debounced search function for combined name+address search
    const debouncedCustomerSearch = debounce(() => {
        if (isConfigEnabled) searchCustomersByCombinedNameAddress(formRef, onCustomerSelect);
    }, 400);

    let form_data = formRef?.current?.getFieldValue();
    let geocoding_loc_data = form_data?.geocoding_location_data?.location;
    return [
        {
            key: 'location',
            label: 'Address',
            colSpan: 4,
            render() {
                return (
                    <>
                        <LocationSearchInput
                            placeholder="Address"
                            useCountryAndID={true}
                            onChange={(address) => {
                                addressFill(address, formRef);
                                if (forceUpdateFn) {
                                    forceUpdateFn();
                                }
                            }}
                            orgSettingsData={orgSettingsData}
                            triggerClear={clearGoogleAddressSearch}
                        />
                    </>
                );
            },
        },
        {
            key: 'clear_fields',
            colSpan: 4,
            label: 'Clear fields',
            render() {
                return (
                    <div className="wy-flex-row-between">
                        {showClearFieldsButton && (
                            <Button
                                type="link"
                                onClick={() => {
                                    clearAddress(formRef);
                                }}
                            >
                                Reset Address
                            </Button>
                        )}
                        {cust_pincode && editMode && (
                            <RefreshGeoLocation
                                srvc_req_id={editorItem?.id}
                                srvc_type_id={editorItem?.srvc_type_id}
                                onChange={() => {
                                    if (onChange) {
                                        onChange(editorItem?.id);
                                    }
                                }}
                                geocoding_loc_data={geocoding_loc_data}
                            />
                        )}
                    </div>
                );
            },
        },
        {
            key: 'cust_line_0',
            colSpan: 1,
            label: 'Flat no',
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch();
            },

            rules: [
                {
                    max: 50,
                },
            ],
        },
        {
            key: 'cust_line_1',
            colSpan: 3,
            label: 'Building/Apartment name',
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch();
            },
            rules: [
                {
                    max: 200,
                },
            ],
        },
        {
            key: 'cust_line_2',
            label: 'Line 1',
            colSpan: 4,
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch();
            },
            rules: [
                {
                    max: 1000,
                },
            ],
        },
        {
            key: 'cust_line_3',
            label: 'Line 2',
            colSpan: 4,
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch();
            },
            rules: [
                {
                    max: 200,
                },
            ],
        },
        {
            key: 'cust_pincode',
            label: 'Pincode',
            colSpan: 2,
            required: is_pincode_mandatory,
            widget: RemoteSourceSelect,
            widgetProps: {
                mode: 'single',
                url: '/searcher',
                placeholder: 'Start typing..',
                params: {
                    fn: 'getPincode',
                },
                widgetProps: {
                    mode: 'single',
                    labelInValue: false,
                    showSearch: true,
                    style: {
                        width: '100%',
                    },
                },
                onChange: (e, option) => {
                    forceUpdateFn();
                    // console.log("pincode val",e);
                    formRef.current.setFieldsValue({
                        cust_pincode: e.split('___')[0],
                        cust_city: option.city,
                        cust_state: option.state,
                    });
                    debouncedCustomerSearch();
                },
            },
            rules: [
                {
                    validator: (_, value) => {
                        if (!value) return Promise.resolve(); // Skip validation if no value is entered
                        if (typeof value === 'string') {
                            value = Number(value.split('___')[0]);
                        }

                        if (!/^\d+$/.test(value)) {
                            return Promise.reject(
                                new Error('Pincode should be a number input.')
                            );
                        }
                        const pincodeLength =
                            orgSettingsData?.selected_country_pincode_length ||
                            6;
                        const regex = new RegExp(`^\\d{${pincodeLength}}$`);
                        if (!regex.test(value)) {
                            return Promise.reject(new Error(`Invalid Pincode`));
                        }
                        return Promise.resolve();
                    },
                },
            ],
        },
        {
            key: 'cust_city',
            label: 'City',
            colSpan: 2,
            widget: RemoteSourceSelect,
            widgetProps: {
                mode: 'single',
                url: '/searcher',
                placeholder: 'Start typing..',
                params: {
                    fn: 'getCities',
                },
                widgetProps: {
                    mode: 'single',
                    labelInValue: false,
                    showSearch: true,
                    style: {
                        width: '100%',
                    },
                },
                onChange: (e, option) => {
                    forceUpdateFn();
                    // console.log("pincode val",e);
                    formRef.current.setFieldsValue({
                        cust_state: option.state,
                    });
                    debouncedCustomerSearch();
                },
            },
        },
        {
            key: 'cust_state',
            label: 'State',
            colSpan: 4,
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch();
            },
            widget: RemoteSourceSelect,
            widgetProps: {
                mode: 'single',
                url: '/searcher',
                placeholder: 'Start typing..',
                params: {
                    fn: 'getState',
                },
                widgetProps: {
                    mode: 'single',
                    labelInValue: false,
                    showSearch: true,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
};

export const getAddressFieldsMetaTMS250312325161 = (
    formRef,
    forceUpdateFn,
    mandatoryAddressFields = [], // Changed from is_pincode_mandatory
    orgSettingsData,
    editMode,
    editorItem,
    onChange,
    cust_pincode,
    isConfigEnabled = false,
    onCustomerSelect = null
) => {
    console.log('yeti called tms250312325161');
    let clearGoogleAddressSearch = new Date().getTime();
    let filledAddress = getConcatenatedAddressFrmForm('cust_', formRef);
    let showClearFieldsButton = filledAddress && filledAddress != '';
    const clearAddress = (formRef) => {
        const keyEmptyValue = {};
        getAddressFieldKeys('cust_').forEach((singleKey) => {
            keyEmptyValue[singleKey] = '';
        });
        clearGoogleAddressSearch = new Date().getTime();
        let result = formRef?.current?.setFieldsValue(keyEmptyValue);
        if (forceUpdateFn) {
            forceUpdateFn();
        }
    };

    // Create a debounced search function for combined name+address search
    const debouncedCustomerSearch = debounce(() => {
        if (isConfigEnabled) searchCustomersByCombinedNameAddress(formRef, onCustomerSelect);
    }, 400);

    let form_data = formRef?.current?.getFieldValue();
    let geocoding_loc_data = form_data?.geocoding_location_data?.location;
    return [
        {
            key: 'location',
            label: 'Address',
            colSpan: 4,
            render() {
                return (
                    <>
                        <LocationSearchInput
                            placeholder="Address"
                            useCountryAndID={true}
                            onChange={(address) => {
                                addressFill(address, formRef);
                                if (forceUpdateFn) {
                                    forceUpdateFn();
                                }
                            }}
                            orgSettingsData={orgSettingsData}
                            triggerClear={clearGoogleAddressSearch}
                        />
                    </>
                );
            },
        },
        {
            key: 'clear_fields',
            colSpan: 4,
            label: 'Clear fields',
            render() {
                return (
                    <div className="wy-flex-row-between">
                        {showClearFieldsButton && (
                            <Button
                                type="link"
                                onClick={() => {
                                    clearAddress(formRef);
                                }}
                            >
                                Reset Address
                            </Button>
                        )}
                        {cust_pincode && editMode && (
                            <RefreshGeoLocation
                                srvc_req_id={editorItem?.id}
                                srvc_type_id={editorItem?.srvc_type_id}
                                onChange={() => {
                                    if (onChange) {
                                        onChange(editorItem?.id);
                                    }
                                }}
                                geocoding_loc_data={geocoding_loc_data}
                            />
                        )}
                    </div>
                );
            },
        },
        {
            key: 'cust_line_0',
            colSpan: 1,
            label: 'Flat no',
            required: mandatoryAddressFields.includes('cust_line_0'),
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch();
            },
            rules: [
                {
                    max: 50,
                },
            ],
        },
        {
            key: 'cust_line_1',
            colSpan: 3,
            label: 'Building/Apartment name',
            required: mandatoryAddressFields.includes('cust_line_1'),
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch()
            },
            rules: [
                {
                    max: 200,
                },
            ],
        },
        {
            key: 'cust_line_2',
            label: 'Line 1',
            colSpan: 4,
            required: mandatoryAddressFields.includes('cust_line_2'),
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch()

            },
            rules: [
                {
                    max: 1000,
                },
            ],
        },
        {
            key: 'cust_line_3',
            label: 'Line 2',
            colSpan: 4,
            required: mandatoryAddressFields.includes('cust_line_3'),
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch()
            },
            rules: [
                {
                    max: 200,
                },
            ],
        },
        {
            key: 'cust_pincode',
            label: 'Pincode',
            colSpan: 2,
            required: mandatoryAddressFields.includes('cust_pincode'),
            widget: RemoteSourceSelect,
            widgetProps: {
                mode: 'single',
                url: '/searcher',
                placeholder: 'Start typing..',
                params: {
                    fn: 'getPincode',
                },
                widgetProps: {
                    mode: 'single',
                    labelInValue: false,
                    showSearch: true,
                    style: {
                        width: '100%',
                    },
                },
                onChange: (e, option) => {
                    forceUpdateFn();
                    formRef.current.setFieldsValue({
                        cust_pincode: e.split('___')[0],
                        cust_city: option.city,
                        cust_state: option.state,
                    });
                debouncedCustomerSearch()
                },
            },
            rules: [
                {
                    validator: (_, value) => {
                        if (!value) return Promise.resolve();
                        if (typeof value === 'string') {
                            value = Number(value.split('___')[0]);
                        }

                        if (!/^\d+$/.test(value)) {
                            return Promise.reject(
                                new Error('Pincode should be a number input.')
                            );
                        }
                        const pincodeLength =
                            orgSettingsData?.selected_country_pincode_length ||
                            6;
                        const regex = new RegExp(`^\\d{${pincodeLength}}$`);
                        if (!regex.test(value)) {
                            return Promise.reject(new Error(`Invalid Pincode`));
                        }
                        return Promise.resolve();
                    },
                },
            ],
        },
        {
            key: 'cust_city',
            label: 'City',
            colSpan: 2,
            required: mandatoryAddressFields.includes('cust_city'),
            widget: RemoteSourceSelect,
            widgetProps: {
                mode: 'single',
                url: '/searcher',
                placeholder: 'Start typing..',
                params: {
                    fn: 'getCities',
                },
                widgetProps: {
                    mode: 'single',
                    labelInValue: false,
                    showSearch: true,
                    style: {
                        width: '100%',
                    },
                },
                onChange: (e, option) => {
                    forceUpdateFn();
                    formRef.current.setFieldsValue({
                        cust_state: option.state,
                    });
                debouncedCustomerSearch()
                },
            },
        },
        {
            key: 'cust_state',
            label: 'State',
            colSpan: 4,
            required: mandatoryAddressFields.includes('cust_state'),
            onChange: () => {
                forceUpdateFn();
                debouncedCustomerSearch()
            },
            widget: RemoteSourceSelect,
            widgetProps: {
                mode: 'single',
                url: '/searcher',
                placeholder: 'Start typing..',
                params: {
                    fn: 'getState',
                },
                widgetProps: {
                    mode: 'single',
                    labelInValue: false,
                    showSearch: true,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
};
