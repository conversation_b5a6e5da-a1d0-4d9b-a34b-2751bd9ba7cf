import { UserOutlined } from '@ant-design/icons';
import { Input, Spin, Tooltip } from 'antd';
import { debounce } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import http_utils from '../../util/http_utils';
import './customerSearchSelect.css';

// Utility function to truncate text with center ellipsis
const displayTruncateCustomerAddressWithCenterEllipsis = (text, maxLength = 40) => {
    if (!text || text.length <= maxLength) return text;

    const start = Math.ceil(maxLength / 2) - 1;
    const end = Math.floor(maxLength / 2) - 2;

    return text.substring(0, start) + '...' + text.substring(text.length - end);
};

const CustomCustomerSearchInput = ({
    form,
    onCustomerSelect,
    onSearchValueChange,
}) => {
    const [options, setOptions] = useState([]);
    const [fetching, setFetching] = useState(false);
    const [dropdownVisible, setDropdownVisible] = useState(false);
    const containerRef = useRef(null);

    // Get current value from form instead of local state
    const getCurrentValue = () => {
        return form.current?.getFieldValue('cust_full_name') || '';
    };

    const fetchCustomers = debounce((query) => {
        if (!query || query.length < 3) return setOptions([]);

        setFetching(true);
        http_utils.performGetCall(
            '/searcher',
            { fn: 'getCustomers', query },
            (resp) => {
                const parsedData =
                    typeof resp.data === 'string'
                        ? JSON.parse(resp.data)
                        : resp.data;

                const customers = parsedData || [];
                setOptions(customers);
                setFetching(false);

                // Check if current search value matches any customer name exactly
                const exactMatch = customers.some(
                    (customer) =>
                        customer.cust_full_name?.toLowerCase() ===
                        query.toLowerCase()
                );
                const isNew = query.length >= 3 && customers.length === 0;
            },
            (error) => {
                console.error('Customer search failed', error);
                setFetching(false);
            }
        );
    }, 400);

    // Fetch customers when form value changes
    useEffect(() => {
        const currentValue = getCurrentValue();
        fetchCustomers(currentValue);
        onSearchValueChange(); // to rerender parent component
    }, []);

    const handleSelect = (customer) => {
        const name = customer.cust_full_name || '';
        form.current.setFieldsValue({ cust_full_name: name });
        if (onCustomerSelect) onCustomerSelect(customer);
        setDropdownVisible(false);
        onSearchValueChange(); // trigger parent rerender
    };

    useEffect(() => {
        const handleOutsideClick = (e) => {
            if (
                containerRef.current &&
                !containerRef.current.contains(e.target)
            ) {
                setDropdownVisible(false);
            }
        };
        document.addEventListener('mousedown', handleOutsideClick);
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
        };
    }, []);

    const renderCustomerOption = (customer) => {
        const displayAddress =
            customer?.full_address ||
            [
                customer?.cust_pincode,
                customer?.cust_line_0,
                customer?.cust_line_1,
                customer?.cust_line_2,
                customer?.cust_line_3,
                customer?.cust_city,
                customer?.cust_state,
            ]
                .filter(Boolean)
                .join(', ');

        return (
            <div
                key={customer?.cust_id}
                onClick={() => handleSelect(customer)}
                className="wy-customer-search-option"
            >
                <div className="wy-customer-details">
                    <div className="wy-customer-flex-wrapper gx-align-items-start">
                        <div>
                            <UserOutlined className="wy-customer-avatar-icon" />
                        </div>
                        <div>
                            <div className="wy-customer-header">
                                <div className="wy-customer-flex-wrapper">
                                    <span className="wy-customer-name-ellipsis">
                                        {customer?.cust_full_name}
                                    </span>
                                </div>
                            </div>
                            <div className="wy-customer-address gx-fs-sm gx-my-1">
                                <div className="wy-customer-flex-wrapper">
                                    <span className="wy-customer-address-text">
                                        <Tooltip title={displayAddress}>
                                            {displayAddress ? (
                                                displayTruncateCustomerAddressWithCenterEllipsis(
                                                    displayAddress,
                                                    40
                                                )
                                            ) : (
                                                <div className="wy-customer-missing-details">
                                                    No address
                                                </div>
                                            )}
                                        </Tooltip>
                                    </span>
                                </div>
                            </div>
                            <div className="wy-customer-flex-wrapper gx-fs-sm">
                                <span className="wy-customer-mobile wy-customer-flex-wrapper">
                                    <Tooltip title={customer?.cust_mobile}>
                                        {customer?.cust_mobile || (
                                            <div className="wy-customer-missing-details">
                                                No mobile number
                                            </div>
                                        )}
                                        ,
                                    </Tooltip>
                                </span>
                                <div className="wy-customer-flex-wrapper">
                                    <div className="wy-customer-email">
                                        {customer?.cust_email || (
                                            <div className="wy-customer-missing-details">
                                                No email address
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div ref={containerRef} className="wy-customer-search-container">
            <Input
                value={getCurrentValue()}
                placeholder="Start typing customer name..."
                onChange={(e) => {
                    const newValue = e.target.value;
                    form.current.setFieldsValue({ cust_full_name: newValue });
                    fetchCustomers(newValue);
                    onSearchValueChange();
                }}
                onFocus={() => setDropdownVisible(true)}
            />
            {dropdownVisible && (
                <div className="wy-customer-search-dropdown">
                    {fetching ? (
                        <div className="wy-customer-search-loading">
                            <Spin size="small" />
                        </div>
                    ) : options.length > 0 ? (
                        options.map(renderCustomerOption)
                    ) : getCurrentValue().length >= 3 ? (
                        <div className="wy-customer-search-no-results">
                            No customers found
                        </div>
                    ) : (
                        <div className="wy-customer-search-message">
                            Type at least 3 characters...
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default CustomCustomerSearchInput;
