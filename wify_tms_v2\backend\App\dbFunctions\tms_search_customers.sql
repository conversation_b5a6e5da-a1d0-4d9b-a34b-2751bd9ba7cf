CREATE OR REPLACE FUNCTION public.tms_search_customers(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE 
    status boolean := false;
    message text := 'Internal_error';
    resp_data json := '{}'::json;
    org_id_ integer;
    query_ text;
    combined_search_ text;
BEGIN
    -- Extract input
    org_id_ = json_extract_path_text(form_data, 'org_id')::int;
    query_ = '%' || coalesce(json_extract_path_text(form_data, 'query'), '') || '%';
    combined_search_ = coalesce(json_extract_path_text(form_data, 'combined_search'), '');

    -- Main logic
    resp_data = (
        SELECT json_agg(result)
        FROM (
            SELECT jsonb_build_object(
                'value', cust.cust_id,
                'label', cust.full_name,
                'cust_full_name', cust.full_name,
                'cust_code', cust.cust_code,
                'cust_email', cust.email,
                'cust_mobile', cust.mobile_num, 
                'cust_line_0', (cust.cust_address).line_0,
                'cust_line_1', (cust.cust_address).line_1,
                'cust_line_2', (cust.cust_address).line_2,
                'cust_line_3', (cust.cust_address).line_3,
                'cust_pincode', (cust.cust_address).pincode,
                'cust_city', (cust.cust_address).city,
                'cust_state', (cust.cust_address).state
            ) AS result
            FROM cl_tx_cust cust
            WHERE cust.org_id = org_id_
            AND (
                (
                    combined_search_ IS NOT NULL AND combined_search_ <> '' AND
                    CONCAT(
                        COALESCE(cust.full_name, ''), ',',
                        COALESCE((cust.cust_address).line_0, ''), ',',
                        COALESCE((cust.cust_address).line_1, ''), ',',
                        COALESCE((cust.cust_address).line_2, ''), ',',
                        COALESCE((cust.cust_address).line_3, ''), ',',
                        COALESCE((cust.cust_address).city, ''), ',',
                        COALESCE((cust.cust_address).state, ''), ',',
                        COALESCE((cust.cust_address).pincode, '')
                    ) ILIKE '%' || combined_search_ || '%'
                )
                OR
                (
                    (combined_search_ IS NULL OR combined_search_ = '') AND (
                        cust.mobile_num ILIKE query_
                        OR cust.email ILIKE query_
                        OR cust.full_name ILIKE query_
                    )
                )
            )
            GROUP BY cust.cust_id
            LIMIT 10
        ) sub
    );

    status := true;
    message := 'success';

    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$;
