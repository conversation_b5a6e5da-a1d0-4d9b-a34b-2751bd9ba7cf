-- DROP FUNCTION public.tms_create_cust(json, uuid);

CREATE OR REPLACE FUNCTION public.tms_create_cust(form_data json, entry_id uuid DEFAULT NULL::uuid)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
	status boolean;
	message text;
	affected_rows integer;
	last_ins_cust_id uuid;
	validation_resp uuid[];
	resp_data json;
	enable_name_based_creation boolean;
	enable_name_based_creation_insert boolean;

--form data 
	cust_org_id_ integer;
	cust_full_name_ text;
	cust_code_ text;
	cust_email_ text;
	cust_mobile_ text;
	cust_telephone_ text;
	cust_line_0_ text;
	cust_line_1_ text;
	cust_line_2_ text;
	cust_line_3_ text;
	cust_pincode_ text;
	cust_city_ text;
	cust_state_ text;
	ip_address_ text;
	user_agent_ text;

begin
	status = false;
	message = 'Internal_error';
	
	--form data 
	cust_org_id_ 	 = json_extract_path_text(form_data,'org_id');
	cust_full_name_  = json_extract_path_text(form_data,'cust_full_name');
	cust_code_       = json_extract_path_text(form_data,'cust_code');
	cust_email_ 	 = json_extract_path_text(form_data,'cust_email'); 
	cust_mobile_ 	 = json_extract_path_text(form_data,'cust_mobile');
    --address (handle NULL values by converting to empty string)
	cust_line_0_  	= COALESCE(json_extract_path_text(form_data,'cust_line_0'), '');
	cust_line_1_  	= COALESCE(json_extract_path_text(form_data,'cust_line_1'), '');
    cust_line_2_  	= COALESCE(json_extract_path_text(form_data,'cust_line_2'), '');
	cust_line_3_ 	= COALESCE(json_extract_path_text(form_data,'cust_line_3'), '');
	cust_pincode_ 	= COALESCE(json_extract_path_text(form_data,'cust_pincode'), '');
	cust_city_ 		= COALESCE(json_extract_path_text(form_data,'cust_city'), '');
	cust_state_ 	= COALESCE(json_extract_path_text(form_data,'cust_state'), '');
	--
	ip_address_ 	= json_extract_path_text(form_data,'ip_address');
	user_agent_ 	= json_extract_path_text(form_data,'user_agent');

	-- Check organization settings to determine validation approach
	SELECT (settings_data->>'enable_customer_profile_creation_based_on_name')::boolean
	INTO enable_name_based_creation
	FROM cl_tx_orgs_settings
	WHERE org_id = cust_org_id_ AND settings_type = 'ORG_LEVEL_SETTINGS';

	-- Default to false if setting not found
	IF enable_name_based_creation IS NULL THEN
		enable_name_based_creation := false;
	END IF;

	RAISE NOTICE 'Customer creation - org_id: %, enable_name_based_creation: %', cust_org_id_, enable_name_based_creation;

	-- Check for duplicates based on organization setting
		IF enable_name_based_creation = true THEN
			-- Name-based validation: check for duplicate name + address combination
			validation_resp := array (
				select cust.cust_id
				  from cl_tx_cust as cust
				 where cust.org_id = cust_org_id_
				   and cust.cust_id <> entry_id
				   and cust.full_name = cust_full_name_
				   and COALESCE((cust.cust_address).line_0, '') = cust_line_0_
				   and COALESCE((cust.cust_address).line_1, '') = cust_line_1_
				   and COALESCE((cust.cust_address).line_2, '') = cust_line_2_
				   and COALESCE((cust.cust_address).pincode, '') = cust_pincode_
			);
		ELSE
			-- Traditional validation: check for duplicate email or mobile
			validation_resp := array ( 
				 select cust.cust_id 
				   from cl_tx_cust as cust 
				  where cust.org_id = cust_org_id_
				    and cust.cust_id <> entry_id
				    and (
				    	cust.email = cust_email_
				     	or cust.mobile_num = cust_mobile_
				    )
			);
		END IF;

	if array_length(validation_resp,1) > 0 then
		status = false;
		-- Set specific error messages based on validation type, keep default for backward compatibility
		IF enable_name_based_creation = true THEN
			message = 'cust_already_exists_name_address';
		ELSE
			message = 'cust_already_exists'; -- Keep existing message for email/mobile validation
		END IF;
		
	elsif entry_id is null then

		-- Check for duplicates again for insert operation
		SELECT (settings_data->>'enable_customer_profile_creation_based_on_name')::boolean
		INTO enable_name_based_creation_insert
		FROM cl_tx_orgs_settings
		WHERE org_id = cust_org_id_ AND settings_type = 'ORG_LEVEL_SETTINGS';

		-- Default to false if setting not found
		IF enable_name_based_creation_insert IS NULL THEN
			enable_name_based_creation_insert := false;
		END IF;
		raise notice ' enable_name_based_creation_insert - % ', enable_name_based_creation_insert;
			-- Check for duplicates based on organization setting
			IF enable_name_based_creation_insert = true THEN
				raise notice ' Checking name and address - % ', enable_name_based_creation_insert;

				
				-- Name-based validation: check for duplicate name + address combination
				validation_resp := array (
					select cust.cust_id
					  from cl_tx_cust as cust
					 where cust.org_id = cust_org_id_
					   and cust.full_name = cust_full_name_
					   and COALESCE((cust.cust_address).line_0, '') = cust_line_0_
					   and COALESCE((cust.cust_address).line_1, '') = cust_line_1_
					   and COALESCE((cust.cust_address).line_2, '') = cust_line_2_
					   and COALESCE((cust.cust_address).pincode, '') = cust_pincode_
				);

				raise notice ' validation_resp - % ', validation_resp;

			ELSE
				-- Traditional validation: check for duplicate email or mobile
				validation_resp := array ( 
					 select cust.cust_id 
					   from cl_tx_cust as cust 
					  where cust.org_id = cust_org_id_
					    and (
					    	cust.email = cust_email_
					     	or cust.mobile_num = cust_mobile_
					    )
				);
			END IF;
		
		if array_length(validation_resp,1) > 0 then
			status = false;
			-- Set specific error messages based on validation type, keep default for backward compatibility
			IF enable_name_based_creation_insert = true THEN
				message = 'cust_already_exists_name_address';
			ELSE
				message = 'cust_already_exists'; -- Keep existing message for email/mobile validation
			END IF;
		else
	
		 	insert into public.cl_tx_cust (
						org_id , full_name , cust_code, email , mobile_num , 
						cust_address, is_active ,c_meta, u_meta )
						values (
							cust_org_id_, 
							cust_full_name_,
							cust_code_,
							cust_email_,
							cust_mobile_,
							(
								cust_line_0_ ,
								cust_line_1_ ,
								cust_line_2_ ,
								cust_line_3_ ,
								cust_pincode_ ,
								cust_city_ ,
								cust_state_ 
							),
							true,
							row(ip_address_,user_agent_,now() at time zone 'utc'),
							row(ip_address_,user_agent_,now() at time zone 'utc')
					   )
					
			returning cust_id into last_ins_cust_id;
			GET DIAGNOSTICS affected_rows = ROW_COUNT;
	--		raise notice 'Affected rows - %',  affected_rows;
			if affected_rows = 1 then
				status = true;
				message = 'success';
				resp_data =  json_build_object('entry_id',last_ins_cust_id);
			end if;
		end if;
	else
	  
		raise notice 'cust_full_name_ %', cust_full_name_;
		raise notice 'entry_id %', entry_id;
	
		update public.cl_tx_cust
		   set "org_id" 	    = cust_org_id_,
		   	   "full_name"  	= cust_full_name_,
			   "cust_code"      = cust_code_,
		       "email"          = cust_email_,
		       "mobile_num"     = cust_mobile_,
		        "cust_address"  = (
									cust_line_0_ ,
									cust_line_1_ ,
									cust_line_2_ ,
									cust_line_3_ ,
									cust_pincode_ ,
									cust_city_ ,
									cust_state_ 
								  ),
		       "u_meta" = row(ip_address_,user_agent_,now() at time zone 'utc') 
		 where cl_tx_cust.cust_id = entry_id;
				
		GET DIAGNOSTICS affected_rows = ROW_COUNT;
--		raise notice 'affected_rows %', affected_rows;
		if affected_rows = 1 then
		 	status = true;
			message = 'success';
			resp_data =  json_build_object('entry_id',entry_id);
		end if;
	
	end if;

	return json_build_object('status',status,'code',message,'data',resp_data);

END;
$function$
;
