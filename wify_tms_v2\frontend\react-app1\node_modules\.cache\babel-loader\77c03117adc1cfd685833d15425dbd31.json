{"ast": null, "code": "import moment from 'moment';\nasync function ruleValidator(formMeta = [], formData = {}, isBulkAssignComp = false) {\n  const errors = [];\n  let forceToChkValidation = false;\n  // console.log('yeti formMeta', formMeta, 'formData ', formData);\n\n  for (const data of [formData]) {\n    const obj = {};\n    const dataErrors = [];\n    for (const field of formMeta) {\n      const {\n        key,\n        label,\n        required,\n        rules = [],\n        widget,\n        options = [],\n        widgetProps\n      } = field;\n      const value = data[key];\n      const fieldErrors = [];\n      const requiredError = validateRequired(value, label, required, isBulkAssignComp);\n      if (requiredError) {\n        fieldErrors.push(requiredError);\n      }\n      if (widget == 'date-picker' && formData.hasOwnProperty(key) && (formData[key] == '' || formData[key] == null)) {\n        forceToChkValidation = true;\n      } else {\n        forceToChkValidation = false;\n      }\n      if (value || forceToChkValidation || rules.length > 0) {\n        const typeError = validateType(value, label, rules);\n        if (typeError) {\n          fieldErrors.push(typeError);\n        }\n        const ruleErrors = await validateRules(value, label, rules, formData, required);\n        if (ruleErrors) {\n          fieldErrors.push(...ruleErrors);\n        }\n        if (value) {\n          const widgetError = validateWidget(value, label, widget);\n          if (widgetError) {\n            fieldErrors.push(widgetError);\n          }\n        }\n        if (options.length > 0) {\n          // this means the the value for this param must be within the options array\n          let optionMismatchError;\n          if (!required && (!value || value.length == 0)) {\n            //do not validate select fields where required is false and no option selected\n            optionMismatchError = undefined;\n          } else {\n            optionMismatchError = validateOptionWithValue(value, label, options, widgetProps);\n          }\n          if (optionMismatchError) {\n            fieldErrors.push(optionMismatchError);\n          }\n        }\n      }\n      if (fieldErrors.length > 0) {\n        obj[key] = value;\n        dataErrors.push(...fieldErrors);\n      }\n    }\n    if (dataErrors.length > 0) {\n      obj.errors = dataErrors;\n      errors.push(obj);\n    }\n  }\n  // console.log('yeti errors', errors);\n  return errors;\n}\nfunction validateRequired(value, label, required, isBulkAssignComp) {\n  if (required && !value) {\n    if (isBulkAssignComp && label == 'Subtask type') {\n      return `${label} is required or invalid or you don't have access to create this subtask.`;\n    }\n    return `${label} is required.`;\n  }\n  return null;\n}\n//validateType: Validates if a value matches a specific data type and returns an error message if the value is not of that type.\nfunction validateType(value, label, rules) {\n  let type;\n  for (const rule of rules) {\n    if (rule.type) {\n      type = rule.type;\n      break;\n    }\n  }\n  if (type && value !== undefined && value !== null) {\n    switch (type) {\n      case 'string':\n        if (typeof value !== 'string') {\n          return `${label} must be a string.`;\n        }\n        break;\n      case 'number':\n        if (typeof value !== 'number') {\n          return `${label} must be a number.`;\n        }\n        break;\n      case 'boolean':\n        if (typeof value !== 'boolean') {\n          return `${label} must be a boolean.`;\n        }\n        break;\n      case 'integer':\n        if (!Number.isInteger(value)) {\n          return `${label} must be an integer.`;\n        }\n        break;\n      case 'float':\n        if (typeof value !== 'number' || Number.isNaN(value) || !Number.isFinite(value)) {\n          return `${label} must be a finite number.`;\n        }\n        break;\n      // case 'date':\n      //     if (!(value instanceof Date) || isNaN(value.getTime())) {\n      //         return `${label} must be a valid date.`;\n      //     }\n      //     break;\n      case 'email':\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\n          return `${label} must be a valid email address.`;\n        }\n        break;\n      default:\n        throw new Error(`Invalid type: ${type}`);\n    }\n  }\n  return null;\n}\n\n//validateRules: Validates if a value matches a specific pattern or meets a certain length requirement and returns an error message if the value does not meet the criteria.\nasync function validateRules(value, label, rules, formData = {}, fieldRequired = false) {\n  const isEmpty = !value;\n  console.log(`AntdRuleValidator :: validateRules :: fieldRequired ${fieldRequired}`);\n  // Early return: if field is not required and value is empty, skip all validation rules\n  if (!fieldRequired && isEmpty) {\n    console.log(`AntdRuleValidator :: validateRules :: returning ${label}`);\n    return null;\n  }\n  const errors = [];\n  for (const rule of rules) {\n    const {\n      message,\n      pattern,\n      min,\n      max,\n      validator,\n      required\n    } = rule;\n    // console.log('yeti rule--->', rule);\n\n    // Pattern Validation\n    if (pattern && typeof pattern === 'string' && !new RegExp(pattern).test(value)) {\n      errors.push(message || `${label} is invalid.`);\n    }\n\n    // Min Length Validation\n    if (min !== undefined && (value === null || value === void 0 ? void 0 : value.length) < min) {\n      errors.push(message || `${label} must be at least ${min} characters.`);\n    }\n\n    // Max Length Validation\n    if (max !== undefined && (value === null || value === void 0 ? void 0 : value.length) > max) {\n      errors.push(message || `${label} must be at most ${max} characters.`);\n    }\n\n    // Required Field Validation\n    if (required && (value === undefined || value === '')) {\n      errors.push(message || `${label} is required.`);\n    }\n\n    // Validator Handling (Asynchronous)\n    if (validator) {\n      try {\n        // Call the async/sync validator\n        const validationResult = await validator({\n          getFieldValue: key => formData[key]\n        }, value);\n        // If it resolves with an error message, add it to the errors\n        if (validationResult) {\n          errors.push(validationResult);\n        }\n      } catch (error) {\n        // If validator throws an error, add the error message\n        errors.push(error.message || error || `${label} validation failed.`);\n      }\n    }\n  }\n  return errors.length > 0 ? errors : null;\n}\nfunction validateOptionWithValue(value, label, options, widgetProps) {\n  const labels = options.map(option => option.label);\n  if (Array.isArray(options) && options.length > 0) {\n    let matched = false;\n\n    // Check if the widget is in multi-select mode\n    if ((widgetProps === null || widgetProps === void 0 ? void 0 : widgetProps.mode) === 'multiple') {\n      // Ensure all selected values are valid options\n      if (Array.isArray(value)) {\n        for (const selectedValue of value) {\n          const isValid = options.some(option => {\n            return typeof option === 'object' ? option.value === selectedValue : option === selectedValue;\n          });\n          if (!isValid) {\n            return `Please enter correct ${label}`;\n          }\n        }\n      } else {\n        // If value is not an array, it's invalid for multi-select\n        return `Please select at least one ${label}`;\n      }\n    } else {\n      // Handle single select scenario\n      matched = options.some(singleOption => {\n        return typeof singleOption === 'object' ? singleOption.value === value : singleOption === value;\n      });\n      if (!matched) {\n        return `Please enter correct ${label}`;\n      }\n    }\n  }\n  return undefined;\n}\nfunction validateWidget(value, label, widget) {\n  switch (widget) {\n    case 'number':\n      if (typeof value !== 'number') {\n        return `${label} must be a number.`;\n      }\n      break;\n    // case 'date-picker':\n    //     if (\n    //         value &&\n    //         !/^\\d{4}-(0[1-9]|1[0-2])-([0-2]\\d|3[01])$/.test(value)\n    //     ) {\n    //         return `${label} must be in YYYY-MM-DD Format.`;\n    //     }\n    //     break;\n  }\n  return undefined;\n}\nexport default ruleValidator;", "map": {"version": 3, "names": ["moment", "ruleValidator", "formMeta", "formData", "isBulkAssignComp", "errors", "forceToChkValidation", "data", "obj", "dataErrors", "field", "key", "label", "required", "rules", "widget", "options", "widgetProps", "value", "fieldErrors", "requiredError", "validateRequired", "push", "hasOwnProperty", "length", "typeError", "validateType", "ruleErrors", "validateRules", "widgetError", "validateWidget", "optionMismatchError", "undefined", "validateOptionWithValue", "type", "rule", "Number", "isInteger", "isNaN", "isFinite", "test", "Error", "fieldRequired", "isEmpty", "console", "log", "message", "pattern", "min", "max", "validator", "RegExp", "validationResult", "getFieldValue", "error", "labels", "map", "option", "Array", "isArray", "matched", "mode", "selected<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "some", "singleOption"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/wify-utils/AntdRuleValidator.js"], "sourcesContent": ["import moment from 'moment';\r\n\r\nasync function ruleValidator(\r\n    formMeta = [],\r\n    formData = {},\r\n    isBulkAssignComp = false\r\n) {\r\n    const errors = [];\r\n    let forceToChkValidation = false;\r\n    // console.log('yeti formMeta', formMeta, 'formData ', formData);\r\n\r\n    for (const data of [formData]) {\r\n        const obj = {};\r\n        const dataErrors = [];\r\n\r\n        for (const field of formMeta) {\r\n            const {\r\n                key,\r\n                label,\r\n                required,\r\n                rules = [],\r\n                widget,\r\n                options = [],\r\n                widgetProps,\r\n            } = field;\r\n            const value = data[key];\r\n            const fieldErrors = [];\r\n\r\n            const requiredError = validateRequired(\r\n                value,\r\n                label,\r\n                required,\r\n                isBulkAssignComp\r\n            );\r\n            if (requiredError) {\r\n                fieldErrors.push(requiredError);\r\n            }\r\n            if (\r\n                widget == 'date-picker' &&\r\n                formData.hasOwnProperty(key) &&\r\n                (formData[key] == '' || formData[key] == null)\r\n            ) {\r\n                forceToChkValidation = true;\r\n            } else {\r\n                forceToChkValidation = false;\r\n            }\r\n            if (value || forceToChkValidation || rules.length > 0) {\r\n                const typeError = validateType(value, label, rules);\r\n                if (typeError) {\r\n                    fieldErrors.push(typeError);\r\n                }\r\n\r\n                const ruleErrors = await validateRules(\r\n                    value,\r\n                    label,\r\n                    rules,\r\n                    formData,\r\n                    required\r\n                );\r\n                if (ruleErrors) {\r\n                    fieldErrors.push(...ruleErrors);\r\n                }\r\n                if (value) {\r\n                    const widgetError = validateWidget(value, label, widget);\r\n                    if (widgetError) {\r\n                        fieldErrors.push(widgetError);\r\n                    }\r\n                }\r\n\r\n                if (options.length > 0) {\r\n                    // this means the the value for this param must be within the options array\r\n                    let optionMismatchError;\r\n                    if (!required && (!value || value.length == 0)) {\r\n                        //do not validate select fields where required is false and no option selected\r\n                        optionMismatchError = undefined;\r\n                    } else {\r\n                        optionMismatchError = validateOptionWithValue(\r\n                            value,\r\n                            label,\r\n                            options,\r\n                            widgetProps\r\n                        );\r\n                    }\r\n                    if (optionMismatchError) {\r\n                        fieldErrors.push(optionMismatchError);\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (fieldErrors.length > 0) {\r\n                obj[key] = value;\r\n                dataErrors.push(...fieldErrors);\r\n            }\r\n        }\r\n\r\n        if (dataErrors.length > 0) {\r\n            obj.errors = dataErrors;\r\n            errors.push(obj);\r\n        }\r\n    }\r\n    // console.log('yeti errors', errors);\r\n    return errors;\r\n}\r\nfunction validateRequired(value, label, required, isBulkAssignComp) {\r\n    if (required && !value) {\r\n        if (isBulkAssignComp && label == 'Subtask type') {\r\n            return `${label} is required or invalid or you don't have access to create this subtask.`;\r\n        }\r\n        return `${label} is required.`;\r\n    }\r\n    return null;\r\n}\r\n//validateType: Validates if a value matches a specific data type and returns an error message if the value is not of that type.\r\nfunction validateType(value, label, rules) {\r\n    let type;\r\n    for (const rule of rules) {\r\n        if (rule.type) {\r\n            type = rule.type;\r\n            break;\r\n        }\r\n    }\r\n\r\n    if (type && value !== undefined && value !== null) {\r\n        switch (type) {\r\n            case 'string':\r\n                if (typeof value !== 'string') {\r\n                    return `${label} must be a string.`;\r\n                }\r\n                break;\r\n            case 'number':\r\n                if (typeof value !== 'number') {\r\n                    return `${label} must be a number.`;\r\n                }\r\n                break;\r\n            case 'boolean':\r\n                if (typeof value !== 'boolean') {\r\n                    return `${label} must be a boolean.`;\r\n                }\r\n                break;\r\n            case 'integer':\r\n                if (!Number.isInteger(value)) {\r\n                    return `${label} must be an integer.`;\r\n                }\r\n                break;\r\n            case 'float':\r\n                if (\r\n                    typeof value !== 'number' ||\r\n                    Number.isNaN(value) ||\r\n                    !Number.isFinite(value)\r\n                ) {\r\n                    return `${label} must be a finite number.`;\r\n                }\r\n                break;\r\n            // case 'date':\r\n            //     if (!(value instanceof Date) || isNaN(value.getTime())) {\r\n            //         return `${label} must be a valid date.`;\r\n            //     }\r\n            //     break;\r\n            case 'email':\r\n                if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\r\n                    return `${label} must be a valid email address.`;\r\n                }\r\n                break;\r\n            default:\r\n                throw new Error(`Invalid type: ${type}`);\r\n        }\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n//validateRules: Validates if a value matches a specific pattern or meets a certain length requirement and returns an error message if the value does not meet the criteria.\r\nasync function validateRules(value, label, rules, formData = {}, fieldRequired = false) {\r\n    const isEmpty = !value;\r\n\r\n    console.log(`AntdRuleValidator :: validateRules :: fieldRequired ${fieldRequired}`)\r\n    // Early return: if field is not required and value is empty, skip all validation rules\r\n    if (!fieldRequired && isEmpty) {\r\n        console.log(`AntdRuleValidator :: validateRules :: returning ${label}`)\r\n        return null;\r\n    }\r\n\r\n    const errors = [];\r\n\r\n    for (const rule of rules) {\r\n        const { message, pattern, min, max, validator, required } = rule;\r\n        // console.log('yeti rule--->', rule);\r\n\r\n        // Pattern Validation\r\n        if (\r\n            pattern &&\r\n            typeof pattern === 'string' &&\r\n            !new RegExp(pattern).test(value)\r\n        ) {\r\n            errors.push(message || `${label} is invalid.`);\r\n        }\r\n\r\n        // Min Length Validation\r\n        if (min !== undefined && value?.length < min) {\r\n            errors.push(\r\n                message || `${label} must be at least ${min} characters.`\r\n            );\r\n        }\r\n\r\n        // Max Length Validation\r\n        if (max !== undefined && value?.length > max) {\r\n            errors.push(\r\n                message || `${label} must be at most ${max} characters.`\r\n            );\r\n        }\r\n\r\n        // Required Field Validation\r\n        if (required && (value === undefined || value === '')) {\r\n            errors.push(message || `${label} is required.`);\r\n        }\r\n\r\n        // Validator Handling (Asynchronous)\r\n        if (validator) {\r\n            try {\r\n                // Call the async/sync validator\r\n                const validationResult = await validator(\r\n                    { getFieldValue: (key) => formData[key] },\r\n                    value\r\n                );\r\n                // If it resolves with an error message, add it to the errors\r\n                if (validationResult) {\r\n                    errors.push(validationResult);\r\n                }\r\n            } catch (error) {\r\n                // If validator throws an error, add the error message\r\n                errors.push(\r\n                    error.message || error || `${label} validation failed.`\r\n                );\r\n            }\r\n        }\r\n    }\r\n    return errors.length > 0 ? errors : null;\r\n}\r\n\r\nfunction validateOptionWithValue(value, label, options, widgetProps) {\r\n    const labels = options.map((option) => option.label);\r\n\r\n    if (Array.isArray(options) && options.length > 0) {\r\n        let matched = false;\r\n\r\n        // Check if the widget is in multi-select mode\r\n        if (widgetProps?.mode === 'multiple') {\r\n            // Ensure all selected values are valid options\r\n            if (Array.isArray(value)) {\r\n                for (const selectedValue of value) {\r\n                    const isValid = options.some((option) => {\r\n                        return typeof option === 'object'\r\n                            ? option.value === selectedValue\r\n                            : option === selectedValue;\r\n                    });\r\n\r\n                    if (!isValid) {\r\n                        return `Please enter correct ${label}`;\r\n                    }\r\n                }\r\n            } else {\r\n                // If value is not an array, it's invalid for multi-select\r\n                return `Please select at least one ${label}`;\r\n            }\r\n        } else {\r\n            // Handle single select scenario\r\n            matched = options.some((singleOption) => {\r\n                return typeof singleOption === 'object'\r\n                    ? singleOption.value === value\r\n                    : singleOption === value;\r\n            });\r\n\r\n            if (!matched) {\r\n                return `Please enter correct ${label}`;\r\n            }\r\n        }\r\n    }\r\n\r\n    return undefined;\r\n}\r\nfunction validateWidget(value, label, widget) {\r\n    switch (widget) {\r\n        case 'number':\r\n            if (typeof value !== 'number') {\r\n                return `${label} must be a number.`;\r\n            }\r\n            break;\r\n        // case 'date-picker':\r\n        //     if (\r\n        //         value &&\r\n        //         !/^\\d{4}-(0[1-9]|1[0-2])-([0-2]\\d|3[01])$/.test(value)\r\n        //     ) {\r\n        //         return `${label} must be in YYYY-MM-DD Format.`;\r\n        //     }\r\n        //     break;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport default ruleValidator;\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAE3B,eAAeC,aAAaA,CACxBC,QAAQ,GAAG,EAAE,EACbC,QAAQ,GAAG,CAAC,CAAC,EACbC,gBAAgB,GAAG,KAAK,EAC1B;EACE,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,oBAAoB,GAAG,KAAK;EAChC;;EAEA,KAAK,MAAMC,IAAI,IAAI,CAACJ,QAAQ,CAAC,EAAE;IAC3B,MAAMK,GAAG,GAAG,CAAC,CAAC;IACd,MAAMC,UAAU,GAAG,EAAE;IAErB,KAAK,MAAMC,KAAK,IAAIR,QAAQ,EAAE;MAC1B,MAAM;QACFS,GAAG;QACHC,KAAK;QACLC,QAAQ;QACRC,KAAK,GAAG,EAAE;QACVC,MAAM;QACNC,OAAO,GAAG,EAAE;QACZC;MACJ,CAAC,GAAGP,KAAK;MACT,MAAMQ,KAAK,GAAGX,IAAI,CAACI,GAAG,CAAC;MACvB,MAAMQ,WAAW,GAAG,EAAE;MAEtB,MAAMC,aAAa,GAAGC,gBAAgB,CAClCH,KAAK,EACLN,KAAK,EACLC,QAAQ,EACRT,gBACJ,CAAC;MACD,IAAIgB,aAAa,EAAE;QACfD,WAAW,CAACG,IAAI,CAACF,aAAa,CAAC;MACnC;MACA,IACIL,MAAM,IAAI,aAAa,IACvBZ,QAAQ,CAACoB,cAAc,CAACZ,GAAG,CAAC,KAC3BR,QAAQ,CAACQ,GAAG,CAAC,IAAI,EAAE,IAAIR,QAAQ,CAACQ,GAAG,CAAC,IAAI,IAAI,CAAC,EAChD;QACEL,oBAAoB,GAAG,IAAI;MAC/B,CAAC,MAAM;QACHA,oBAAoB,GAAG,KAAK;MAChC;MACA,IAAIY,KAAK,IAAIZ,oBAAoB,IAAIQ,KAAK,CAACU,MAAM,GAAG,CAAC,EAAE;QACnD,MAAMC,SAAS,GAAGC,YAAY,CAACR,KAAK,EAAEN,KAAK,EAAEE,KAAK,CAAC;QACnD,IAAIW,SAAS,EAAE;UACXN,WAAW,CAACG,IAAI,CAACG,SAAS,CAAC;QAC/B;QAEA,MAAME,UAAU,GAAG,MAAMC,aAAa,CAClCV,KAAK,EACLN,KAAK,EACLE,KAAK,EACLX,QAAQ,EACRU,QACJ,CAAC;QACD,IAAIc,UAAU,EAAE;UACZR,WAAW,CAACG,IAAI,CAAC,GAAGK,UAAU,CAAC;QACnC;QACA,IAAIT,KAAK,EAAE;UACP,MAAMW,WAAW,GAAGC,cAAc,CAACZ,KAAK,EAAEN,KAAK,EAAEG,MAAM,CAAC;UACxD,IAAIc,WAAW,EAAE;YACbV,WAAW,CAACG,IAAI,CAACO,WAAW,CAAC;UACjC;QACJ;QAEA,IAAIb,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,IAAIO,mBAAmB;UACvB,IAAI,CAAClB,QAAQ,KAAK,CAACK,KAAK,IAAIA,KAAK,CAACM,MAAM,IAAI,CAAC,CAAC,EAAE;YAC5C;YACAO,mBAAmB,GAAGC,SAAS;UACnC,CAAC,MAAM;YACHD,mBAAmB,GAAGE,uBAAuB,CACzCf,KAAK,EACLN,KAAK,EACLI,OAAO,EACPC,WACJ,CAAC;UACL;UACA,IAAIc,mBAAmB,EAAE;YACrBZ,WAAW,CAACG,IAAI,CAACS,mBAAmB,CAAC;UACzC;QACJ;MACJ;MAEA,IAAIZ,WAAW,CAACK,MAAM,GAAG,CAAC,EAAE;QACxBhB,GAAG,CAACG,GAAG,CAAC,GAAGO,KAAK;QAChBT,UAAU,CAACa,IAAI,CAAC,GAAGH,WAAW,CAAC;MACnC;IACJ;IAEA,IAAIV,UAAU,CAACe,MAAM,GAAG,CAAC,EAAE;MACvBhB,GAAG,CAACH,MAAM,GAAGI,UAAU;MACvBJ,MAAM,CAACiB,IAAI,CAACd,GAAG,CAAC;IACpB;EACJ;EACA;EACA,OAAOH,MAAM;AACjB;AACA,SAASgB,gBAAgBA,CAACH,KAAK,EAAEN,KAAK,EAAEC,QAAQ,EAAET,gBAAgB,EAAE;EAChE,IAAIS,QAAQ,IAAI,CAACK,KAAK,EAAE;IACpB,IAAId,gBAAgB,IAAIQ,KAAK,IAAI,cAAc,EAAE;MAC7C,OAAO,GAAGA,KAAK,0EAA0E;IAC7F;IACA,OAAO,GAAGA,KAAK,eAAe;EAClC;EACA,OAAO,IAAI;AACf;AACA;AACA,SAASc,YAAYA,CAACR,KAAK,EAAEN,KAAK,EAAEE,KAAK,EAAE;EACvC,IAAIoB,IAAI;EACR,KAAK,MAAMC,IAAI,IAAIrB,KAAK,EAAE;IACtB,IAAIqB,IAAI,CAACD,IAAI,EAAE;MACXA,IAAI,GAAGC,IAAI,CAACD,IAAI;MAChB;IACJ;EACJ;EAEA,IAAIA,IAAI,IAAIhB,KAAK,KAAKc,SAAS,IAAId,KAAK,KAAK,IAAI,EAAE;IAC/C,QAAQgB,IAAI;MACR,KAAK,QAAQ;QACT,IAAI,OAAOhB,KAAK,KAAK,QAAQ,EAAE;UAC3B,OAAO,GAAGN,KAAK,oBAAoB;QACvC;QACA;MACJ,KAAK,QAAQ;QACT,IAAI,OAAOM,KAAK,KAAK,QAAQ,EAAE;UAC3B,OAAO,GAAGN,KAAK,oBAAoB;QACvC;QACA;MACJ,KAAK,SAAS;QACV,IAAI,OAAOM,KAAK,KAAK,SAAS,EAAE;UAC5B,OAAO,GAAGN,KAAK,qBAAqB;QACxC;QACA;MACJ,KAAK,SAAS;QACV,IAAI,CAACwB,MAAM,CAACC,SAAS,CAACnB,KAAK,CAAC,EAAE;UAC1B,OAAO,GAAGN,KAAK,sBAAsB;QACzC;QACA;MACJ,KAAK,OAAO;QACR,IACI,OAAOM,KAAK,KAAK,QAAQ,IACzBkB,MAAM,CAACE,KAAK,CAACpB,KAAK,CAAC,IACnB,CAACkB,MAAM,CAACG,QAAQ,CAACrB,KAAK,CAAC,EACzB;UACE,OAAO,GAAGN,KAAK,2BAA2B;QAC9C;QACA;MACJ;MACA;MACA;MACA;MACA;MACA,KAAK,OAAO;QACR,IAAI,CAAC,4BAA4B,CAAC4B,IAAI,CAACtB,KAAK,CAAC,EAAE;UAC3C,OAAO,GAAGN,KAAK,iCAAiC;QACpD;QACA;MACJ;QACI,MAAM,IAAI6B,KAAK,CAAC,iBAAiBP,IAAI,EAAE,CAAC;IAChD;EACJ;EAEA,OAAO,IAAI;AACf;;AAEA;AACA,eAAeN,aAAaA,CAACV,KAAK,EAAEN,KAAK,EAAEE,KAAK,EAAEX,QAAQ,GAAG,CAAC,CAAC,EAAEuC,aAAa,GAAG,KAAK,EAAE;EACpF,MAAMC,OAAO,GAAG,CAACzB,KAAK;EAEtB0B,OAAO,CAACC,GAAG,CAAC,uDAAuDH,aAAa,EAAE,CAAC;EACnF;EACA,IAAI,CAACA,aAAa,IAAIC,OAAO,EAAE;IAC3BC,OAAO,CAACC,GAAG,CAAC,mDAAmDjC,KAAK,EAAE,CAAC;IACvE,OAAO,IAAI;EACf;EAEA,MAAMP,MAAM,GAAG,EAAE;EAEjB,KAAK,MAAM8B,IAAI,IAAIrB,KAAK,EAAE;IACtB,MAAM;MAAEgC,OAAO;MAAEC,OAAO;MAAEC,GAAG;MAAEC,GAAG;MAAEC,SAAS;MAAErC;IAAS,CAAC,GAAGsB,IAAI;IAChE;;IAEA;IACA,IACIY,OAAO,IACP,OAAOA,OAAO,KAAK,QAAQ,IAC3B,CAAC,IAAII,MAAM,CAACJ,OAAO,CAAC,CAACP,IAAI,CAACtB,KAAK,CAAC,EAClC;MACEb,MAAM,CAACiB,IAAI,CAACwB,OAAO,IAAI,GAAGlC,KAAK,cAAc,CAAC;IAClD;;IAEA;IACA,IAAIoC,GAAG,KAAKhB,SAAS,IAAI,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,MAAM,IAAGwB,GAAG,EAAE;MAC1C3C,MAAM,CAACiB,IAAI,CACPwB,OAAO,IAAI,GAAGlC,KAAK,qBAAqBoC,GAAG,cAC/C,CAAC;IACL;;IAEA;IACA,IAAIC,GAAG,KAAKjB,SAAS,IAAI,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,MAAM,IAAGyB,GAAG,EAAE;MAC1C5C,MAAM,CAACiB,IAAI,CACPwB,OAAO,IAAI,GAAGlC,KAAK,oBAAoBqC,GAAG,cAC9C,CAAC;IACL;;IAEA;IACA,IAAIpC,QAAQ,KAAKK,KAAK,KAAKc,SAAS,IAAId,KAAK,KAAK,EAAE,CAAC,EAAE;MACnDb,MAAM,CAACiB,IAAI,CAACwB,OAAO,IAAI,GAAGlC,KAAK,eAAe,CAAC;IACnD;;IAEA;IACA,IAAIsC,SAAS,EAAE;MACX,IAAI;QACA;QACA,MAAME,gBAAgB,GAAG,MAAMF,SAAS,CACpC;UAAEG,aAAa,EAAG1C,GAAG,IAAKR,QAAQ,CAACQ,GAAG;QAAE,CAAC,EACzCO,KACJ,CAAC;QACD;QACA,IAAIkC,gBAAgB,EAAE;UAClB/C,MAAM,CAACiB,IAAI,CAAC8B,gBAAgB,CAAC;QACjC;MACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;QACZ;QACAjD,MAAM,CAACiB,IAAI,CACPgC,KAAK,CAACR,OAAO,IAAIQ,KAAK,IAAI,GAAG1C,KAAK,qBACtC,CAAC;MACL;IACJ;EACJ;EACA,OAAOP,MAAM,CAACmB,MAAM,GAAG,CAAC,GAAGnB,MAAM,GAAG,IAAI;AAC5C;AAEA,SAAS4B,uBAAuBA,CAACf,KAAK,EAAEN,KAAK,EAAEI,OAAO,EAAEC,WAAW,EAAE;EACjE,MAAMsC,MAAM,GAAGvC,OAAO,CAACwC,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAAC7C,KAAK,CAAC;EAEpD,IAAI8C,KAAK,CAACC,OAAO,CAAC3C,OAAO,CAAC,IAAIA,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;IAC9C,IAAIoC,OAAO,GAAG,KAAK;;IAEnB;IACA,IAAI,CAAA3C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4C,IAAI,MAAK,UAAU,EAAE;MAClC;MACA,IAAIH,KAAK,CAACC,OAAO,CAACzC,KAAK,CAAC,EAAE;QACtB,KAAK,MAAM4C,aAAa,IAAI5C,KAAK,EAAE;UAC/B,MAAM6C,OAAO,GAAG/C,OAAO,CAACgD,IAAI,CAAEP,MAAM,IAAK;YACrC,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAC3BA,MAAM,CAACvC,KAAK,KAAK4C,aAAa,GAC9BL,MAAM,KAAKK,aAAa;UAClC,CAAC,CAAC;UAEF,IAAI,CAACC,OAAO,EAAE;YACV,OAAO,wBAAwBnD,KAAK,EAAE;UAC1C;QACJ;MACJ,CAAC,MAAM;QACH;QACA,OAAO,8BAA8BA,KAAK,EAAE;MAChD;IACJ,CAAC,MAAM;MACH;MACAgD,OAAO,GAAG5C,OAAO,CAACgD,IAAI,CAAEC,YAAY,IAAK;QACrC,OAAO,OAAOA,YAAY,KAAK,QAAQ,GACjCA,YAAY,CAAC/C,KAAK,KAAKA,KAAK,GAC5B+C,YAAY,KAAK/C,KAAK;MAChC,CAAC,CAAC;MAEF,IAAI,CAAC0C,OAAO,EAAE;QACV,OAAO,wBAAwBhD,KAAK,EAAE;MAC1C;IACJ;EACJ;EAEA,OAAOoB,SAAS;AACpB;AACA,SAASF,cAAcA,CAACZ,KAAK,EAAEN,KAAK,EAAEG,MAAM,EAAE;EAC1C,QAAQA,MAAM;IACV,KAAK,QAAQ;MACT,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAO,GAAGN,KAAK,oBAAoB;MACvC;MACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EACA,OAAOoB,SAAS;AACpB;AAEA,eAAe/B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}