require('dotenv').config();
const AWS = require('aws-sdk');
const mime = require('mime-types');
const momentTimeZone = require('moment-timezone');
const path = require('path');
const { geminiApi } = require('../../utils/geminiApi');
const {
    getSubtaskRatingModelFrQueue,
} = require('./helpers/subtask_rating_helper');

AWS.config.update({
    accessKeyId: process.env.S3_ACCESS_KEY,
    secretAccessKey: process.env.S3_SECRET_KEY,
    region: process.env.S3_REGION,
});

const s3 = new AWS.S3();

const getLabelForKey = ({ statusKeyMap, key }) => {
    if (!statusKeyMap) {
        return key;
    }
    const matchingObject = statusKeyMap.find((item) => item.key === key);

    return matchingObject ? matchingObject.label : key;
};

async function downloadAndEncodeImage({ objectKey }) {
    try {
        const decodedObjectKey = decodeURIComponent(objectKey);
        const s3Response = await s3
            .getObject({
                Bucket: process.env.S3_BUCKET,
                Key: decodedObjectKey,
            })
            .promise();
        const base64Image = s3Response.Body.toString('base64');
        const mimeType = mime.lookup(objectKey) || 'application/octet-stream';
        return { base64Image, mimeType };
    } catch (error) {
        console.error(
            'gai :: gai_rating_for_technician_subtask :: Error in downloadAndEncodeImage:',
            error
        );
        throw error;
    }
}

async function processAllMedia({ mediaObj, statusKeyMap }) {
    let encodedImages = {};
    const allowedExtensions = [
        '.jpg',
        '.jpeg',
        '.png',
        '.img',
        '.webp',
        '.bmp',
    ];

    for (const [key, filePaths] of Object.entries(mediaObj)) {
        const label = getLabelForKey({ statusKeyMap, key });

        if (!encodedImages[label]) {
            encodedImages[label] = [];
        }

        for (const filePath of filePaths) {
            const fileName = path.basename(filePath);
            const fileExtension = path.extname(fileName).toLowerCase();

            if (!allowedExtensions.includes(fileExtension)) {
                console.log(
                    `gai :: gai_rating_for_technician_subtask :: Skipping file with unsupported extension: ${fileName}`
                );
                continue;
            }

            try {
                const encodedImage = await downloadAndEncodeImage({
                    objectKey: filePath,
                });
                encodedImages[label].push(encodedImage);
            } catch (error) {
                console.log(
                    `gai :: gai_rating_for_technician_subtask :: Error processing ${filePath}:`,
                    error
                );
            }
        }
    }

    return encodedImages;
}

function parseJsonStrings(input) {
    try {
        return JSON.parse(input, (_, value) =>
            typeof value === 'string' ? parseJsonStrings(value) : value
        );
    } catch (error) {
        return input;
    }
}

function generateFeedbackPrompt({ feedbackForm }) {
    const formData = feedbackForm.form_data;
    const formMeta = feedbackForm.form_meta;
    const resultObject = {};

    formMeta.forEach((item) => {
        if (formData.hasOwnProperty(item.key)) {
            resultObject[item.label] = formData[item.key];
        }
    });

    if (!resultObject || Object.keys(resultObject).length === 0) {
        return null;
    }

    let prompt = `To compile feedback from users regarding my task completion, I've summarized their input as follows:`;

    Object.entries(resultObject).forEach(([label, value]) => {
        prompt += `${label}: ${value}\n`;
    });
    prompt += `USER SATISFICATION: 0\n`;
    prompt += '\n';

    return prompt;
}

function getDateTime({ datetimeStr, timezone }) {
    if (!datetimeStr) {
        return { date: null, time: null };
    }
    const localTime = momentTimeZone.utc(datetimeStr).tz(timezone);
    const formatedLocalTime = localTime.format('YYYY-MM-DD HH:mm');
    const [formattedDate, formattedTime] = formatedLocalTime.split(' ');

    return {
        date: formattedDate,
        time: formattedTime,
    };
}

const generateRatingForSubTask = async ({ taskData }) => {
    taskData = parseJsonStrings(taskData);

    console.log(
        'gai :: gai_rating_for_technician_subtask :: generateRatingForSubTask :: taskData :: ',
        taskData
    );
    let absolutePrompt = '';
    let realtivePrompt = '';
    const timezone = taskData['org_timezone']
        ? taskData['org_timezone']
        : 'Asia/Kolkata';
    const { date: startDate, time: startTime } = getDateTime({
        datetimeStr: taskData['Start time'],
        timezone,
    });
    const { date: endDate, time: endTime } = getDateTime({
        datetimeStr: taskData['End time'],
        timezone,
    });
    const { date: plannedStartDate, time: plannedStartTime } = getDateTime({
        datetimeStr: taskData['Pl. start time'],
        timezone,
    });
    const { date: plannedEndDate, time: plannedEndTime } = getDateTime({
        datetimeStr: taskData['Pl. end time'],
        timezone,
    });

    let taskDescriptionAbsolutePrompt = `
  I was assigned a task, which I have completed. Here's a brief overview of the task details and how I executed it:-\n
    Planned Start Day: ${plannedStartDate}
    Actual Start Day: ${startDate}
    \n`;

    let taskDescriptionRelativePrompt = `
  I was assigned a task, which I have completed. Here's a brief overview of the task details and how I executed it:-\n
    Task Description: ${taskData['Description']}
    Planned Start Day: ${plannedStartDate}
    Planned Start Time: ${plannedStartTime}
    Actual Start Day: ${startDate}
    Actual Start Time: ${startTime}
    Start Remarks: ${taskData['Start remarks']}
    Planned End Date: ${plannedEndDate}
    Planned End Time: ${plannedEndTime}
    Actual End Date: ${endDate}
    Actual End Time: ${endTime}
    End Remarks: ${taskData['End remarks']}
    \n`;

    absolutePrompt = absolutePrompt + taskDescriptionAbsolutePrompt;
    realtivePrompt = realtivePrompt + taskDescriptionRelativePrompt;
    // console.log('gai :: gai_rating_for_technician_subtask :: generateRatingForSubTask :: absolutePrompt :: ', absolutePrompt);

    const feedbackFormMeta = taskData['Feedback data'];
    if (feedbackFormMeta && feedbackFormMeta != null) {
        feedbackPrompt = generateFeedbackPrompt({
            feedbackForm: feedbackFormMeta,
        });
        if (feedbackPrompt) {
            realtivePrompt = realtivePrompt + feedbackPrompt;
        }
    }
    // console.log('gai :: gai_rating_for_technician_subtask :: generateRatingForSubTask :: after feedback :: absolutePrompt :: ', absolutePrompt);

    let encodedImages = {};
    // if (taskData[ 'Start files' ]) {
    //   const _encodedImages = await processAllMedia({ mediaObj: taskData[ 'Start files' ], statusKeyMap: taskData[ 'start status key map' ] })
    //   encodedImages = { ...encodedImages, ..._encodedImages }
    // }
    // console.log('gai :: gai_rating_for_technician_subtask :: generateRatingForSubTask :: after Start files :: absolutePrompt :: ', absolutePrompt);

    if (taskData['End files']) {
        const _encodedImages = await processAllMedia({
            mediaObj: taskData['End files'],
            statusKeyMap: taskData['end status key map'],
        });
        encodedImages = { ...encodedImages, ..._encodedImages };
    }
    // console.log('gai :: gai_rating_for_technician_subtask :: generateRatingForSubTask :: after end files :: absolutePrompt :: ', absolutePrompt);

    let images = [];

    if (encodedImages && Object.keys(encodedImages).length > 0) {
        let imagesPrompt = 'Refer images taken during closure : ';

        Object.keys(encodedImages).forEach((key) => {
            const _images = encodedImages[key];
            imagesPrompt += ` ${key},`;
            images = [...images, ..._images];
        });

        absolutePrompt += imagesPrompt;
        realtivePrompt += imagesPrompt;
    }

    const absoluteRulesPrompt = `
  \n Rate my task based on the following assessment criteria:-
  1. Image Quality (weightage 30%):
    a. Good: One or more pictures uploaded.
    b. Bad: No pictures uploaded.
    (If there is no image uploaded, rate this as Bad. Do not assume anything beyond the provided data.)

  2. User Satisfaction (weightage 40%) :
    a. If user has given the good rating, then give good ratings.
    b. If user has given the bad rating, then give bad ratings.

  3. Task Completion (weightage 30%):
    a. Good: If the planned Start Date and actual start date are same.
    b. Bad: If the planned Start Date and actual start date are not same.
  \n
  (Please remember: Do not hallucinate or assume anything. Only give your analysis based on the data provided, without making any inferences.)
`;

    const relativeRulesPrompt = `
  \n Rate my task based on the following assessment criteria:-
  1. Time Management (weightage 25%):
    a. Bad: Actual start time is later than planned start time; actual end time is later than planned end time.
    b. Good: Actual end time is earlier than planned end time.
  2. Updates (weightage 25%):
    a. Good: All updates done by the technician within +/- 15 minutes of planned start and end time of task.
    b. Bad: Updates not done by the technician or start and end times are very close.
  3. Image Quality (weightage 25%):
    a. Good: Two or more clear, aligned pictures uploaded.
    b. Bad: Less than two pictures, misaligned/shabby pictures, timestamp mismatch beyond 60 minutes.
  4. Productivity (weightage 25%):
    a. Good: Job completed in less time than expected.
    b. Bad: Time taken is +50% more than expected. \n
`;

    absolutePrompt += absoluteRulesPrompt;
    realtivePrompt += relativeRulesPrompt;

    const responsePrompt = `
    Only give the response as json format {rating: < whole number between 1 to 5>, 'description': < minimum 2 line string text with no numberings or font style >}.
  `;

    absolutePrompt += responsePrompt;
    realtivePrompt += responsePrompt;
    // console.log('gai :: gai_rating_for_technician_subtask :: absolutePrompt :: ', absolutePrompt);

    let allEncodedImagesInGeminiFormat = [];

    for (const _eachImg of images) {
        const imageDataString = JSON.stringify(allEncodedImagesInGeminiFormat);
        if (imageDataString.length > 900000) break;

        allEncodedImagesInGeminiFormat.push({
            inline_data: {
                data: _eachImg.base64Image,
                mime_type: _eachImg.mimeType,
            },
        });
    }

    let geminiAbsolute = {};
    let geminiRelative = {};
    const geminiAbsoluteResponse = await geminiApi({
        textQuery: absolutePrompt,
        endFiles: taskData['End files'] || {},
    });
    console.log(
        'gai :: gai_rating_for_technician_subtask :: geminiAbsoluteResponse :: ',
        geminiAbsoluteResponse
    );
    if (geminiAbsoluteResponse?.rating || geminiAbsoluteResponse?.description) {
        geminiAbsolute = {
            rating: geminiAbsoluteResponse?.rating || 'NA',
            description: geminiAbsoluteResponse?.description || 'NA',
        };
    }
    const geminiRelativeResponse = await geminiApi({
        textQuery: realtivePrompt,
        endFiles: taskData['End files'] || {},
    });
    // console.log('gai :: gai_rating_for_technician_subtask :: geminiRelativeResponse :: ', geminiRelativeResponse);
    if (geminiRelativeResponse?.rating || geminiRelativeResponse?.description) {
        geminiRelative = {
            rating: geminiRelativeResponse?.rating || 'NA',
            description: geminiRelativeResponse?.description || 'NA',
        };
    }

    let _pushGaiRatingToDbData = {
        sbtsk_db_id: taskData?.['sbtsk_db_id'],
        org_id: taskData?.['org_id'],
        srvc_req_db_id: taskData?.['srvc_req_db_id'],
        sbtsk_type_id: taskData?.['sbtsk_type_id'],
        order_id: taskData?.['Order ID'],
    };

    if (
        geminiAbsolute.rating == 'NA' ||
        geminiAbsolute.description == 'NA' ||
        !geminiAbsoluteResponse ||
        geminiAbsoluteResponse.error
    ) {
        console.log(
            'gai :: gai_rating_for_technician_subtask :: rating NOT done for :: absolute :: ',
            taskData?.['Order ID'],
            taskData?.['sbtsk_db_id']
        );
        geminiAbsolute = null;
    } else {
        _pushGaiRatingToDbData['absoluteGaiRating'] = geminiAbsolute;
    }

    if (
        geminiRelative.rating == 'NA' ||
        geminiRelative.description == 'NA' ||
        !geminiRelativeResponse ||
        geminiRelativeResponse.error
    ) {
        console.log(
            'gai :: gai_rating_for_technician_subtask :: rating NOT done for :: relative :: ',
            taskData?.['Order ID'],
            taskData?.['sbtsk_db_id']
        );
        geminiRelative = null;
    } else {
        _pushGaiRatingToDbData['relativeGaiRating'] = geminiRelative;
    }

    if (geminiAbsolute == null || geminiRelative == null) {
        return;
    }

    await pushGaiRatingToDb(_pushGaiRatingToDbData);
};

const pushGaiRatingToDb = async ({
    absoluteGaiRating,
    relativeGaiRating,
    sbtsk_db_id,
    org_id,
    srvc_req_db_id,
    sbtsk_type_id,
    order_id,
}) => {
    try {
        // console.log('gai :: gai_rating_for_technician_subtask ::  pushGaiRatingToDb :: ', order_id);
        const app = require('./../../../app');
        const subtaskRatingModel = getSubtaskRatingModelFrQueue(app, {});
        const updateSubtask =
            await subtaskRatingModel.updateSubtaskGaiRatingData({
                order_id,
                absoluteGaiRating,
                relativeGaiRating,
                sbtsk_db_id,
                org_id,
                srvc_req_db_id,
                sbtsk_type_id,
            });
        // console.log('gai :: gai_rating_for_technician_subtask :: pushGaiRatingToDb :: updateSubtask :: ', updateSubtask);
        if (updateSubtask) {
            console.log(
                'gai :: gai_rating_for_technician_subtask :: rating done for :: sbtsk_db_id :: ',
                order_id,
                sbtsk_db_id
            );
        }
    } catch (error) {
        console.log(
            'gai :: gai_rating_for_technician_subtask :: pushGaiRatingToDb :: error :: ',
            order_id,
            error
        );
    }
};

function delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}

const performJob = async (job, done) => {
    // console.log('gai :: gai_rating_for_technician_subtask :: Job for gai rating for subtask started', job?.data);
    try {
        if (job.data) {
            // console.log('gai :: gai_rating_for_technician_subtask :: performJob :: job.data :: ', job.data);
            await generateRatingForSubTask({ taskData: job.data });
            await delay(10000); //10s delay
        }
    } catch (error) {
        console.log(
            'gai :: gai_rating_for_technician_subtask :: performJob :: error :: ',
            error
        );
    } finally {
        done(null, {});
    }
};

exports.default = performJob;
