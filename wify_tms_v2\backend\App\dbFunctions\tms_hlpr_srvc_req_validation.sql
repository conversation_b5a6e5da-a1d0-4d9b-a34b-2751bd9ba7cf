CREATE OR REPLACE FUNCTION public.tms_hlpr_srvc_req_validation(_config_data json, orgs_level_settings_data json, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE 
    status bool;
    message text;
    code text;

    cust_mobile_ text;
    cust_pincode_ text;
    cust_line_0_ text;
    cust_line_1_ text;
    cust_line_2_ text;
    cust_line_3_ text;
    cust_city_ text;
    cust_state_ text;
    service_provider_id_ text;
    is_srvc_req_updation boolean default false;
    request_req_date text;
   --collab_order_id
	collab_order_id_ text;
	srvc_type_id_ integer;
    org_id_ integer;
    feature_access_fr_TMS250312325161 boolean;

BEGIN
    --Default set
    status = true;
    message = 'success';

    cust_mobile_       	 = form_data_->>'cust_mobile';
    cust_pincode_      	 = form_data_->>'cust_pincode';
    cust_line_0_       	 = form_data_->>'cust_line_0';
    cust_line_1_       	 = form_data_->>'cust_line_1';
    cust_line_2_       	 = form_data_->>'cust_line_2';
    cust_line_3_       	 = form_data_->>'cust_line_3';
    cust_city_         	 = form_data_->>'cust_city';
    cust_state_        	 = form_data_->>'cust_state';
    service_provider_id_ = form_data_->>'service_provider_id';
    request_req_date = form_data_->>'request_req_date';
    --collab_order_id
    collab_order_id_     = form_data_->>'79a88c7b-c64f-46c4-a277-bc80efa1c154';
    srvc_type_id_        = form_data_->>'srvc_type_id';
    org_id_              = form_data_->>'org_id';

   	feature_access_fr_TMS250312325161 = tms_hlpr_get_feature_access_fr_current_usr(form_data_, 'TMS250312325161');
   
    if collab_order_id_ is null and form_data_->>'ext_order_id' is not null then
   		collab_order_id_ = form_data_->>'ext_order_id';
    end if;
    
    if form_data_->>'tms_display_code' is not null or 
        tms_hlpr_is_collab_order_id_exists(org_id_, srvc_type_id_, collab_order_id_) is true then
        is_srvc_req_updation = true;
    end if;

    if is_srvc_req_updation is false then

        if cust_mobile_ IS NULL AND (_config_data->>'srvc_cust_mobile_not_mandatory' IS NULL OR (_config_data->>'srvc_cust_mobile_not_mandatory')::bool IS FALSE) then 
	        status = false;
	        code = 'invalid_mobile';
	        message = 'Mobile no. mandatory';
        elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_pincode_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_pincode'
			  ) then
			    status := false;
			    code := 'invalid_pincode';
			    message := 'Pincode mandatory';
	    ELSIF feature_access_fr_TMS250312325161 is not TRUE AND cust_pincode_ IS NULL AND (_config_data->>'srvc_req_cust_pincode_mandatory')::bool IS TRUE then
            status = false;
	        code = 'invalid_pincode';
	        message = 'Pincode mandatory';
	    elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_line_0_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_line_0'
			  ) then
			status = false;
	        code = 'invalid_flat_no';
	        message = 'Flat no mandatory';
	    elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_line_1_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_line_1'
			  ) then
			status = false;
	        code = 'invalid_builiding';
	        message = 'Building/Apartment name mandatory';
	    elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_line_2_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_line_2'
			  ) then
			status = false;
	        code = 'invalid_line_1';
	        message = 'Line 1 mandatory';
	    elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_line_3_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_line_3'
			  ) then
			status = false;
	        code = 'invalid_line_2';
	        message = 'Line 2 mandatory';
	    elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_city_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_city'
			  ) then
			status = false;
	        code = 'invalid_cust_city';
	        message = 'City mandatory';
	    elsif feature_access_fr_TMS250312325161 IS TRUE AND cust_state_ IS NULL 
			  AND EXISTS (
			        SELECT 1 
			        FROM jsonb_array_elements_text((_config_data->'select_mandatory_address_fields_for_a_request')::jsonb) AS field(value)
			        WHERE value = 'cust_state'
			  ) then
			status = false;
	        code = 'invalid_cust_state';
	        message = 'State mandatory';
        end if;     

        if status is false then 
            RETURN json_build_object('status', status, 'code', code, 'message', message);
        end if;

    end if;

   --We need to ensure that the service provider ID is not empty and only accepts integers.
    IF service_provider_id_ = '' THEN
        status = false;
        code = 'invalid_srvc_prvdr_id';
        message = 'Invalid service provider id';

    ELSIF NOT tms_hlpr_is_valid_integer_value(service_provider_id_) THEN
        status = false;
        code = 'invalid_srvc_prvdr_id';
        message = 'Service provider id should be an integer';

    ELSIF cust_mobile_ <> '' THEN
        IF NOT tms_hlpr_is_valid_integer_value(cust_mobile_) THEN
            status = false;
            code = 'invalid_mobile';
            message = 'Mobile no. should contain only numbers';

        ELSIF LENGTH(cust_mobile_) <> (orgs_level_settings_data->>'select_number_of_digits_for_the_phone_number')::int THEN
            status = false;
            code = 'invalid_mobile';
            message = 'Mobile no. must be ' || (orgs_level_settings_data->>'select_number_of_digits_for_the_phone_number')::int || ' digits';
        END IF;
       
    ELSIF cust_pincode_ <> '' THEN
        IF NOT tms_hlpr_is_valid_integer_value(cust_pincode_) THEN
            status = false;
            code = 'invalid_pincode';
            message = 'Pincode should contain only numbers';

        ELSIF LENGTH(cust_pincode_) <> (orgs_level_settings_data->>'selected_country_pincode_length')::int THEN
            status = false;
            code = 'invalid_pincode';
            message = 'Pincode must be ' || (orgs_level_settings_data->>'selected_country_pincode_length')::int || ' digits';
        END IF;
       
    ELSIF request_req_date is null and (_config_data->>'request_service_date_mandatory_while_request_creation')::bool IS true THEN
        status = false;
        code = 'invalid_req_date';
        message = 'Request date needed to be added';
       
    END IF;

    -- Finalize status and message
    RETURN json_build_object('status', status, 'code', code, 'message', message);

END;
$function$
;
